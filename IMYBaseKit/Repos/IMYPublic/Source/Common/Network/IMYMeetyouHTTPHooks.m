//
//  IMYMeetyouHTTPHooks.m
//  IMYPublic
//
//  Created by mario on 16/4/9.
//  Copyright © 2016年 meiyou. All rights reserved.
//

#import "IMYMeetyouHTTPHooks.h"
#import "IMYHTTPDNS.h"
#import "IMYPublic.h"
#import "UIDevice+CAID.h"
#import <CommonCrypto/NSData+CommonCrypto.h>
#import <CocoaSecurity/Base64.h>
#import <pthread/pthread.h>
#import "IMYHTTPBuildable+IMYNetworkingPrivate.h"
#import "IMYHTTPBackupHostConfig.h"

#if __has_include(<IMYDynamicFrameworks/WXApi.h>)
#import <IMYDynamicFrameworks/WXApi.h>
#endif

#import <IOC-Protocols/IOCAppInfo.h>
#import <IOC-Protocols/IOCCAIDManager.h>

// 网络性能监控相关导入
#import "IMYNetworkPerformanceMetrics.h"
#import "IMYNetworkPerformanceMonitor.h"
#import <objc/runtime.h>

static NSString *kIMYCurrentPageName = @"LaunchScreen";
static NSString *kIMYCurrentPageSource = @"LaunchScreen";
static NSString *kIMYCurrentPageHistory = @"LaunchScreen";
static NSString *kIMYCurrentMainTab = @"LaunchScreen";
static uint64_t kIMYCurrentPageTime = 0;

BOOL IMYWechatLogoutAlertShowing = NO;

@protocol IMYMeetyouHTTPHooks_IMYUTExposureStatus
- (id)imyut_exposureStatus;
- (uint64_t)pageTime;
+ (NSString *)imy_userAgent;
@end

@interface UIViewController (IMYMeetyouHTTPHooks)

- (void)IMYMeetyouHTTPHooks_viewDidAppear:(BOOL)animated;

@end

@implementation UIViewController (IMYMeetyouHTTPHooks)

- (void)IMYMeetyouHTTPHooks_viewDidAppear:(BOOL)animated {
    [self IMYMeetyouHTTPHooks_viewDidAppear:animated];
    NSString *className = NSStringFromClass(self.class);
    if ([className hasPrefix:@"UI"] || [className hasPrefix:@"_UI"]) {
        // 过滤系统 VC
        return;
    }
    [self imyhttphook_setupCurrentPageHistory];
    [self imyhttphook_setupCurrentPageSource];
    [self imyhttphook_setupMainTab];
    [self imyhttphook_setupPageTime];
}

- (void)imyhttphook_setupCurrentPageHistory {
    /// 覆盖的 Window 不统计
    UIWindow *const rootWindow = [UIApplication sharedApplication].delegate.window;
    UIWindow *const showWindow = self.view.window;
    if (!rootWindow || !showWindow) {
        return;
    }
    if (showWindow != rootWindow) {
        return;
    }

    // 寻找 父VC
    UIViewController *parentVC = self;
    while (parentVC.parentViewController && parentVC.parentViewController != parentVC.navigationController) {
        parentVC = parentVC.parentViewController;
    }

    if ([parentVC isKindOfClass:[UINavigationController class]] ||
        [parentVC isKindOfClass:[UITabBarController class]]) {
        return;
    }

    static IMYWeakObject *lastVC = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        lastVC = [IMYWeakObject weakObject:nil];
    });
    // 同一个页面，重复显示，不统计
    if (lastVC.obj == parentVC) {
        return;
    }
    lastVC.obj = parentVC;

    NSString *pageName = [(id<IMYGAEventProtocol>)parentVC ga_pageName];
    if (imy_isBlankString(pageName)) {
        return;
    }

    NSString *history = kIMYCurrentPageHistory;
    NSMutableArray *components = [[history componentsSeparatedByString:@"->"] mutableCopy];
    [components addObject:pageName];
    if (components.count > 3) {
        [components removeObjectAtIndex:0];
    }
    kIMYCurrentPageHistory = [components componentsJoinedByString:@"->"];
    imy_asyncMainBlock(1, ^{
        [history class];
    });
}

- (void)imyhttphook_setupCurrentPageSource {
    /// 覆盖的 Window 不统计
    UIWindow *const rootWindow = [UIApplication sharedApplication].delegate.window;
    UIWindow *const showWindow = self.view.window;
    if (!rootWindow || !showWindow) {
        return;
    }
    if (showWindow != rootWindow) {
        return;
    }

    // 寻找 父VC
    UIViewController *parentVC = self;
    while (parentVC.parentViewController && parentVC.parentViewController != parentVC.navigationController) {
        parentVC = parentVC.parentViewController;
    }

    if ([parentVC isKindOfClass:[UINavigationController class]] ||
        [parentVC isKindOfClass:[UITabBarController class]]) {
        return;
    }

    static IMYWeakObject *lastVC = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        lastVC = [IMYWeakObject weakObject:nil];
    });
    // 同一个页面，重复显示，不统计
    if (lastVC.obj == parentVC) {
        return;
    }
    lastVC.obj = parentVC;

    // 页面堆栈信息
    NSString * const pageName = [(id<IMYGAEventProtocol>)parentVC ga_pageName];
    
    NSString *source = pageName;
    UIViewController *preViewController = [parentVC imy_beforeViewController];
    if (preViewController) {
        source = [NSString stringWithFormat:@"%@->%@", [(id<IMYGAEventProtocol>)preViewController ga_pageName], pageName];
    }

    /// 延迟释放，防止出现野指针
    id temp = kIMYCurrentPageSource;
    kIMYCurrentPageSource = source;
    
    id temp2 = kIMYCurrentPageName;
    kIMYCurrentPageName = pageName;
    
    imy_asyncMainBlock(1, ^{
        [temp class];
        [temp2 class];
    });
}

- (void)imyhttphook_setupMainTab {
    /// 覆盖的 Window 不统计
    UIWindow *const rootWindow = [UIApplication sharedApplication].delegate.window;
    UIWindow *const showWindow = self.view.window;
    if (!rootWindow || !showWindow) {
        return;
    }
    if (showWindow != rootWindow) {
        return;
    }

    // 寻找 父VC
    UIViewController *parentVC = self;
    while (parentVC.parentViewController && parentVC.parentViewController != parentVC.navigationController) {
        parentVC = parentVC.parentViewController;
    }

    if ([parentVC isKindOfClass:[UINavigationController class]] ||
        [parentVC isKindOfClass:[UITabBarController class]]) {
        return;
    }

    static IMYWeakObject *lastVC = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        lastVC = [IMYWeakObject weakObject:nil];
    });
    // 同一个页面，重复显示，不统计
    if (lastVC.obj == parentVC) {
        return;
    }
    lastVC.obj = parentVC;

    // 计算页面堆栈
    UITabBarController * const rootVC = rootWindow.rootViewController;
    if ([rootVC isKindOfClass:UITabBarController.class] || [rootVC isKindOfClass:UINavigationController.class]) {
        NSMutableArray *pageList = [NSMutableArray array];
        void (^appendPage)(id) = ^(id page) {
            if (!page) {
                return;
            }
            NSString *pageName = [(id<IMYGAEventProtocol>)page ga_pageName];
            [pageList addObject:pageName];
        };
        void (^repeatFind)(id) = ^(id page) {
            UINavigationController *navVC = page;
            while (navVC != nil) {
                if ([navVC isKindOfClass:[UINavigationController class]]) {
                    for (UIViewController *vc in navVC.viewControllers) {
                        appendPage(vc);
                    }
                } else {
                    appendPage(navVC);
                }
                navVC = navVC.presentedViewController;
            }
        };
        UINavigationController * const navVC = (UINavigationController *)([rootVC isKindOfClass:UITabBarController.class] ? rootVC.selectedViewController : rootVC);
        repeatFind(navVC);
        if (pageList.count > 0) {
            if (pageList.count > 20) {
                pageList = [pageList subarrayWithRange:NSMakeRange(0, 20)];
            }
            /// 延迟释放，防止出现野指针
            id temp = kIMYCurrentMainTab;
            kIMYCurrentMainTab = [pageList componentsJoinedByString:@"->"];
            imy_asyncMainBlock(1, ^{
                [temp class];
            });
        }
    }
}

- (void)imyhttphook_setupPageTime {
    UIWindow *const rootWindow = [UIApplication sharedApplication].delegate.window;
    UIWindow *const showWindow = self.view.window;
    /// 覆盖的 Window 不统计
    if (showWindow == rootWindow) {
        if ([self isKindOfClass:[UINavigationController class]] ||
            [self isKindOfClass:[UITabBarController class]]) {
            return;
        }
        if ([self respondsToSelector:@selector(imyut_exposureStatus)]) {
            kIMYCurrentPageTime = [[(id)self imyut_exposureStatus] pageTime];
        } else {
            kIMYCurrentPageTime = IMYDateTimeIntervalSince1970() * 1000;
        }
    }
}

@end


@interface IMYMeetyouHTTPHooks ()
@property (nonatomic, strong, readonly) NSMutableDictionary *headersHookMaps;
@property (nonatomic, strong, readonly) NSMutableDictionary *queriesHookMaps;
@property (nonatomic, strong, readonly) NSMutableDictionary *statinfoHookMaps;
@property (nonatomic, strong, readonly) NSMutableDictionary *willRequestHookMaps;
@property (nonatomic, strong, readonly) NSMutableDictionary *didSuccessHookMaps;
@property (nonatomic, strong, readonly) NSMutableDictionary *didFailHookMaps;

@property (nonatomic, copy) NSArray<NSDictionary *(^)(NSString *)> *headersHooks;
@property (nonatomic, copy) NSArray<NSDictionary *(^)(NSString *)> *queriesHooks;
@property (nonatomic, copy) NSArray<NSDictionary *(^)(NSString *, NSDictionary *)> *statinfoHooks;
@property (nonatomic, copy) NSArray<void (^)(NSMutableURLRequest *)> *willRequestHooks;
@property (nonatomic, copy) NSArray<void (^)(IMYHTTPResponse *)> *didSuccessHooks;
@property (nonatomic, copy) NSArray<void (^)(NSError *)> *didFailHooks;

@property (nonatomic, copy, readonly) NSArray *gzipHosts;
@property (nonatomic, copy, readonly) NSArray *aesHosts;
@end

@implementation IMYMeetyouHTTPHooks {
    BOOL _setupedHosts;
}
@synthesize gzipHosts = _gzipHosts;
@synthesize aesHosts = _aesHosts;

IMY_KYLIN_FUNC_METHOD_SWIZZLE {
    [UIViewController imy_swizzleMethod:@selector(viewDidAppear:)
                             withMethod:@selector(IMYMeetyouHTTPHooks_viewDidAppear:)
                                  error:nil];
    // This is now automatically handled by the +load method in the NSURLSession category below
    // [IMYMeetyouHTTPHooks enableNSURLSessionAutoHook];
}

+ (NSString *)currentPageName {
    NSString *name = kIMYCurrentPageName;
    return name;
}

+ (NSString *)currentPageSource {
    NSString *source = kIMYCurrentPageSource;
    return source;
}

+ (NSString *)currentPageHistory {
    NSString *history = kIMYCurrentPageHistory;
    return history;
}

+ (NSString *)currentMainTab {
    NSString *maintab = kIMYCurrentMainTab;
    return maintab;
}

+ (uint64_t)currentPageTime {
    if (0 == kIMYCurrentPageTime) {
        kIMYCurrentPageTime = IMYDateTimeIntervalSince1970() * 1000;
    }
    return kIMYCurrentPageTime;
}

+ (NSString *)userTokenInvalidNotification {
    return @"1213Notification";
}

+ (NSString *)phoneInvalidNotification {
    return @"phoneInvalidNotification";
}

+ (NSString *)userTokenExpiredNotification {
    return @"IMYMeetyouHTTPHooks.userTokenExpiredNotification";
};

+ (IMYMeetyouHTTPHooks *)sharedInstance {
    static dispatch_once_t once;
    static id instance;
    dispatch_once(&once, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _setupedHosts = NO;
        _headersHookMaps = [NSMutableDictionary dictionary];
        _queriesHookMaps = [NSMutableDictionary dictionary];
        _statinfoHookMaps = [NSMutableDictionary dictionary];
        _willRequestHookMaps = [NSMutableDictionary dictionary];
        _didSuccessHookMaps = [NSMutableDictionary dictionary];
        _didFailHookMaps = [NSMutableDictionary dictionary];
    }
    return self;
}

+ (RACSignal *)getCurrentTimestamp {
    return [[IMYPublicServerRequest getPath:@"v2/init" host:users_seeyouyima_com params:nil headers:nil] flattenMap:^RACStream *(IMYHTTPResponse *x) {
        NSDictionary *dict = x.responseObject;
        if ([dict isKindOfClass:[NSDictionary class]] && dict[@"timestamp"]) {
            return [RACSignal return:dict[@"timestamp"]];
        } else {
            return [RACSignal error:[NSError errorWithDomain:@"v2/init" code:-1 userInfo:nil]];
        }
    }];
}

- (NSArray *)gzipHosts {
    if (!_setupedHosts) {
        [self setupAnyHosts];
    }
    return _gzipHosts;
}

- (NSArray *)aesHosts {
    if (!_setupedHosts) {
        [self setupAnyHosts];
    }
    return _aesHosts;
}

- (void)setupAnyHosts {
    if (_setupedHosts) {
        return;
    }
    _setupedHosts = YES;
    [self updateAnyHosts];
    [[IMYDoorManager sharedManager].dataLoadFinishedSignal subscribeNext:^(id x) {
        [self updateAnyHosts];
    }];
}

- (void)updateAnyHosts {
    [self updateGzipHosts];
    [self updateAESHosts];
    [self removeAESHostsFromGzip];
}

- (void)updateGzipHosts {
    IMYSwitchModel *switchModel = [[IMYDoorManager sharedManager] switchForType:@"enable_gzip"];
    id temp = _gzipHosts;
    if (switchModel.status) {
        _gzipHosts = switchModel.data.list;
    } else {
        _gzipHosts = nil;
    }
    // 防止多线程野指针问题
    imy_asyncMainBlock(1, ^{
        [temp class];
    });
}

- (void)updateAESHosts {
    IMYSwitchModel *switchModel = [[IMYDoorManager sharedManager] switchForType:@"enable_aes_host"];
    id temp = _aesHosts;
    if (switchModel.status) {
        _aesHosts = switchModel.data.list;
    } else {
        _aesHosts = nil;
    }
    // 防止多线程野指针问题
    imy_asyncMainBlock(1, ^{
        [temp class];
    });
}

- (void)removeAESHostsFromGzip {
    NSMutableArray *removeList = [NSMutableArray array];
    for (NSString *url in _gzipHosts) {
        for (NSString *aesHost in _aesHosts) {
            if ([url isEqualToString:aesHost]) {
                [removeList addObject:url];
                break;
            }
        }
    }
    if (removeList.count > 0) {
        NSMutableArray *gzipHosts = [NSMutableArray arrayWithArray:_gzipHosts];
        [gzipHosts removeObjectsInArray:removeList];
        _gzipHosts = [gzipHosts copy];
    }
}

+ (void)addDefaultHeaders:(NSDictionary *)headers {
    NSDictionary *returnMap = [headers copy];
    NSString *key = [NSString stringWithFormat:@"headers_%p", returnMap];
    [self addHeadersHook:^NSDictionary *(NSString *urlString) {
        return returnMap;
    } forKey:key];
}

#pragma mark - Private

+ (BOOL)needAESWithHost:(NSString *)urlHost {
    NSArray *list = [self sharedInstance].aesHosts;
    BOOL needAES = NO;
    for (NSString *host in list) {
        if ([urlHost isEqualToString:host]) {
            needAES = YES;
            break;
        }
    }
    return needAES;
}

+ (BOOL)isV2URLWithHost:(NSString *)host andPath:(NSString *)path {
    // 该域名列表下，都是V2接口
    static NSArray *hostV2List;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        hostV2List = @[
            @"mgo.seeyouyima.com",
            @"mate.seeyouyima.com",
            @"msp.seeyouyima.com",
            @"sub.seeyouyima.com",
            @"my-survey.seeyouyima.com",
        ];
    });
    // 判断是否满足域名要求
    for (NSString *v2key in hostV2List) {
        if (host.length > 0 && [host containsString:v2key]) {
            return YES;
        }
        if (!host.length && path.length > 0 && [path containsString:v2key]) {
            return YES;
        }
    }
    
    BOOL v2 = ([path hasPrefix:@"v2/"] || [path hasPrefix:@"/v2/"] ||
               [path hasPrefix:@"v3/"] || [path hasPrefix:@"/v3/"] ||
               [path hasPrefix:@"v4/"] || [path hasPrefix:@"/v4/"] ||
               [path hasPrefix:@"v5/"] || [path hasPrefix:@"/v5/"]);
    
    BOOL ownSite = host.length > 0 ? [IMYMeetyouHTTPHooks isMeiYouSite:host] : [IMYMeetyouHTTPHooks isMeiYouSite:path];
    return v2 && ownSite;
}

+ (BOOL)isV2UrlString:(NSString *)urlString {
    NSURL *url = [NSURL imy_URLWithString:urlString];
    return [self isV2URLWithHost:url.host andPath:url.path];
}

+ (BOOL)isV2Buildable:(IMYHTTPBuildable *)buildable {
    return [self isV2URLWithHost:buildable.host andPath:buildable.path];
}

+ (NSMutableDictionary *)addtionalHeadersWithUrlString:(NSString * const)urlString
                                            apiHeaders:(NSDictionary * const)apiHeaders {
    if ([self isV2UrlString:urlString]) {
        return [self addtionalHeadersWithUrlString_v2:urlString 
                                           apiHeaders:apiHeaders];
    } else {
        return [self addtionalHeadersWithUrlString_old:urlString
                                            apiHeaders:apiHeaders];
    }
}

+ (NSMutableDictionary *)addtionalHeadersWithUrlString_old:(NSString * const)urlString
                                                apiHeaders:(NSDictionary * const)apiHeaders {
    if (!urlString.length) {
        return nil;
    }
    NSMutableDictionary * const headers = [NSMutableDictionary dictionary];
    if ([self isMeiYouSite:urlString]) {
        IMYPublicAppHelper *appHelper = [IMYPublicAppHelper shareAppHelper];
        headers[@"myclient"] = appHelper.myclient;
        if (NSBundle.enableMYAppInfo) {
            headers[@"myappinfo"] = appHelper.myappinfo;
        }
        headers[@"buildv"] = APPBuildVersion;
        headers[@"appid"] = appHelper.app_id;
        headers[@"scale"] = [NSString stringWithFormat:@"%.1f", SCREEN_SCALE];
        headers[@"session-id"] = [IMYGAEventHelper sessionID];

        // 授权头
        NSDictionary *authMap = [IMYMeetyouHTTPHooks currentAuthorizationMap];
        [headers addEntriesFromDictionary:authMap];
        
        // statInfo
        NSDictionary *statInfoHeaders = [self old_statInfoHeadersWithUrlString:urlString apiHeaders:apiHeaders];
        [headers addEntriesFromDictionary:statInfoHeaders];
        
        // 追加ab信息
        IMYABTestManager *abManager = [IMYABTestManager sharedInstance];
        if (abManager.exp_log) {
            headers[@"exp"] = abManager.exp_log;
        }
        if (abManager.isol_log) {
            headers[@"isol"] = abManager.isol_log;
        }
        // 关闭了个性化推荐
        if (NO == appHelper.isPersonalRecommand) {
            headers[@"recomm"] = @"0";
        }
        // 青少年模式
        if (appHelper.useYoungMode) {
            headers[@"young"] = @"1";
        }
        // 关闭了个性化广告
        if (NO == appHelper.isPersonalAD) {
            headers[@"open-person-ad"] = @"2";
        }
        
        // 关闭了电商推荐
        if (!appHelper.isPersonalEBRecommend) {
            headers[@"open-person-eb-recomm"] = @"2";
        }
        
        // 关闭了搜索推荐
        if (IMYHIVE_BINDER(IOCAppInfo).isClosedSearchRecommend) {
            headers[@"open-person-search-recom"] = @"2";
        }
        
        // 第二身份Header, 7.6.9孕期需求
        IMYUserInfoModel *userSecondModel = appHelper.userSecondModel;
        if (userSecondModel.userMode) {
            headers[@"mode2"] = @(userSecondModel.userMode).stringValue;
        }
        if (userSecondModel.baby_id) {
            headers[@"bbid"] = @(userSecondModel.baby_id).stringValue;
        }
        if (imy_isNotBlankString(userSecondModel.birthday)) {
            headers[@"bbday"] = userSecondModel.birthday;
        }
#ifdef DEBUG
        headers[@"isDebug"] = @"1";
#endif
    }
    return headers;
}

+ (NSDictionary *)currentAuthorizationMap {
    // 授权头
    IMYPublicAppHelper *appHelper = [IMYPublicAppHelper shareAppHelper];
    if (imy_isNotBlankString(appHelper.userToken)) {
        NSString *authorizationString = nil;
        if ([appHelper.userToken hasPrefix:@"XDS "]) {
            authorizationString = appHelper.userToken;
        } else {
            authorizationString = [NSString stringWithFormat:@"XDS %@", appHelper.userToken];
        }
        return @{@"Authorization" : authorizationString};
    } else if (imy_isNotBlankString(appHelper.virtualToken)) {
        // 更改逻辑：只有没找到授权头才传虚拟授权头。 之前逻辑：虚拟授权头 有就传
        NSString *authorizationString = nil;
        if ([appHelper.virtualToken hasPrefix:@"VDS "]) {
            authorizationString = appHelper.virtualToken;
        } else {
            authorizationString = [NSString stringWithFormat:@"VDS %@", appHelper.virtualToken];
        }
        return @{@"Authorization-Virtual" : authorizationString};
    }
    return nil;
}

+ (NSMutableDictionary *)addtionalHeadersWithUrlString_v2:(NSString *)urlString apiHeaders:(NSDictionary *)apiHeaders {
    if (!urlString.length) {
        return nil;
    }
    NSMutableDictionary * const headers = [NSMutableDictionary dictionary];
    if ([self isMeiYouSite:urlString]) {
        IMYPublicAppHelper *appHelper = [IMYPublicAppHelper shareAppHelper];
        headers[@"myclient"] = appHelper.myclient;
        if (NSBundle.enableMYAppInfo) {
            headers[@"myappinfo"] = appHelper.myappinfo;
        }
        headers[@"scale"] = [NSString stringWithFormat:@"%.1f", SCREEN_SCALE];
        headers[@"lang"] = appHelper.language;
        headers[@"themeid"] = appHelper.themeID;
        headers[@"mode"] = [NSString stringWithFormat:@"%ld", (long)appHelper.userMode];
        headers[@"session-id"] = [IMYGAEventHelper sessionID];

        // 页面跳转信息
        headers[@"source"] = [self currentPageSource];

        // 授权头
        NSDictionary *authMap = [IMYMeetyouHTTPHooks currentAuthorizationMap];
        [headers addEntriesFromDictionary:authMap];
        
        // statInfo
        NSDictionary *statInfoHeaders = [self v2_statInfoHeadersWithUrlString:urlString apiHeaders:apiHeaders];
        [headers addEntriesFromDictionary:statInfoHeaders];
        
        // 追加ab信息
        IMYABTestManager *abManager = [IMYABTestManager sharedInstance];
        if (abManager.exp_log) {
            headers[@"exp"] = abManager.exp_log;
        }
        if (abManager.isol_log) {
            headers[@"isol"] = abManager.isol_log;
        }
        // 关闭了个性化推荐
        if (NO == appHelper.isPersonalRecommand) {
            headers[@"recomm"] = @"0";
        }
        // 青少年模式
        if (appHelper.useYoungMode) {
            headers[@"young"] = @"1";
        }
        // 关闭了个性化广告
        if (NO == appHelper.isPersonalAD) {
            headers[@"open-person-ad"] = @"2";
        }
        
        // 关闭了电商推荐
        if (!appHelper.isPersonalEBRecommend) {
            headers[@"open-person-eb-recomm"] = @"2";
        }
        
        if (IMYHIVE_BINDER(IOCAppInfo).isClosedSearchRecommend) {
            headers[@"open-person-search-recom"] = @"2";
        }
        
        if (IMYHIVE_BINDER(IOCAppInfo).clang) {
            headers[@"clang"] = IMYHIVE_BINDER(IOCAppInfo).clang;
        }
        
        // 第二身份Header, 7.6.9孕期需求
        IMYUserInfoModel *userSecondModel = appHelper.userSecondModel;
        if (userSecondModel.userMode) {
            headers[@"mode2"] = @(userSecondModel.userMode).stringValue;
        }
        if (userSecondModel.baby_id) {
            headers[@"bbid"] = @(userSecondModel.baby_id).stringValue;
        }
        if (imy_isNotBlankString(userSecondModel.birthday)) {
            headers[@"bbday"] = userSecondModel.birthday;
        }
        //最小宝宝信息
        if (userSecondModel.min_baby_id) {
            headers[@"min-bbid"] = @(userSecondModel.min_baby_id).stringValue;
        }
        if (imy_isNotBlankString(userSecondModel.min_birthday)) {
            headers[@"min-bbday"] = userSecondModel.min_birthday;
        }
        if (userSecondModel.x_visit_mode) {
            headers[@"x-visit-mode"] = @(userSecondModel.x_visit_mode).stringValue;
        }
        
#ifdef DEBUG
        headers[@"isDebug"] = @"1";
#endif
    }
    return headers;
}

+ (NSDictionary *)statInfoHeadersForUrlString:(NSString *)urlString {
    if ([self isV2UrlString:urlString]) {
        return [self v2_statInfoHeadersWithUrlString:urlString apiHeaders:nil];
    } else {
        return [self old_statInfoHeadersWithUrlString:urlString apiHeaders:nil];
    }
}

+ (NSDictionary *)statInfoEncryptHeadersForInfoDict:(NSDictionary *)infoDict urlHost:(NSString *)urlHost {
#ifdef DEBUG
    // 过滤测试环境的域名前缀
    NSArray *filterPrefix = @[@"test-qa-", @"test-", @"yf-", @"dev-"];
    for (NSString *prefix in filterPrefix) {
        if ([urlHost hasPrefix:prefix]) {
            urlHost = [urlHost substringFromIndex:prefix.length];
            break;
        }
    }
#endif
    // 一定要有值
    if (!urlHost.length) {
        urlHost = @"*";
    }
    BOOL ignoreAppending = NO;
    NSData *infoJsonData = [infoDict imy_jsonData];
    NSDictionary *statInfoHeaders = nil;
    // aes encode
    do {
        if (!infoJsonData.length) {
            // 无json数据
            break;
        }
        IMYCConfigsGroup * const ccGroup = [[IMYConfigsCenter sharedInstance] groupForKeyPath:@"meetyou_app_setting.st_config"];
        if (![ccGroup boolForKey:@"status"]) {
            // 总开关未开启
            break;
        }
        NSString *ccKey = [ccGroup stringForKey:@"key"];
        if (!ccKey.length) {
            // 无配置aeskey
            break;
        }
        NSString *ccVer = [ccGroup stringForKey:@"v"];
        if (!ccVer.length) {
            // 无配置aeskey版本号
            break;
        }
        static pthread_mutex_t _lock;
        static IMYStashObject *stashMap = nil;
        static dispatch_once_t onceToken;
        dispatch_once(&onceToken, ^{
            stashMap = [IMYStashObject new];
            pthread_mutex_init(&_lock, NULL);
        });
        NSString *bhsStr = [ccGroup stringForKey:@"bhs"];
        NSString *ignoreStr = [ccGroup stringForKey:@"ignore"];
        NSString *hashCode = [NSString stringWithFormat:@"%@+%@+%@+%@", ccKey, ccVer, bhsStr, ignoreStr];
        
        pthread_mutex_lock(&_lock);
        
        BOOL isHashEqual = [hashCode isEqualToString:stashMap.obj1_copy];
        if (!isHashEqual) {
            // 保存 hash code
            stashMap.obj1_copy = hashCode;
            // key 倒序
            stashMap.obj2 = [ccKey imy_reversedString];
            // 域名黑名单，用逗号分隔
            NSArray *blackHosts = [bhsStr componentsSeparatedByString:@","];
            stashMap.obj3 = (blackHosts.count > 0 ? [NSSet setWithArray:blackHosts] : nil);
            // statinfo忽略名单，用逗号分隔
            NSArray *ignoreHosts = [ignoreStr componentsSeparatedByString:@","];
            stashMap.obj4 = (ignoreHosts.count > 0 ? [NSSet setWithArray:ignoreHosts] : nil);
        }
        NSString *aesKey = stashMap.obj2;
        NSSet *blackSets = stashMap.obj3;
        NSSet *ignoreSets = stashMap.obj4;
        
        pthread_mutex_unlock(&_lock);
        
        if ([ignoreSets containsObject:urlHost]) {
            // 命中忽略白名单
            ignoreAppending = YES;
            break;
        }
        if ([blackSets containsObject:urlHost]) {
            // 命中域名黑名单，不加密
            break;
        }
        // 采用AES256加密
        static NSString *const aesIV = @"AST378JKD98FGTRr";
        NSData *encryptData = [NSData imy_encryptData:infoJsonData aes256Key:aesKey iv:aesIV];
        NSString *base64 = [encryptData base64EncodedStringWithOptions:0];
        if (base64.length > 0) {
            statInfoHeaders = @{
                @"statinfo" : base64,
                @"sv" : ccVer
            };
        }
    } while (0);
    
    if (ignoreAppending) {
        // 不追加 statinfo
        statInfoHeaders = @{};
    } else if (!statInfoHeaders) {
        // 如果无生成对应 headers，则使用不加密的方式
        NSString *base64 = [infoJsonData base64EncodedStringWithOptions:0];
        statInfoHeaders = @{
            @"statinfo" : base64 ?: @"",
        };
    }
    return statInfoHeaders;
}

+ (NSMutableDictionary *)old_statInfoMapWithAD:(const BOOL)isAD {
    NSMutableDictionary *infoDict = [NSMutableDictionary dictionaryWithCapacity:64];
    IMYPublicAppHelper *appHelper = [IMYPublicAppHelper shareAppHelper];
    ///系统参数
    {
        infoDict[@"ot"] = [UIDevice imy_carrierName];
        infoDict[@"ua"] = [UIDevice imy_platform];
        infoDict[@"webua"] = [[UIDevice class] imy_userAgent];
        
        if ([[UIDevice imy_machineModel] containsString:@"iPad"]) {
            infoDict[@"os"] = @(5);
        } else {
            infoDict[@"os"] = @(1);
        }

        infoDict[@"mac"] = [UIDevice imy_realMacAddress];
        
        infoDict[@"idfa"] = [UIDevice imy_idfaString] ?: @"";
        infoDict[@"aaid"] = [UIDevice imy_AAID];
        infoDict[@"caid"] = [IMYHIVE_BINDER(IOCCAIDManager) getCacheCAID] ?: @"";
        
        infoDict[@"openudid"] = [UIDevice imy_openUDID];
        infoDict[@"umengid"] = [UIDevice imy_umengID];
        infoDict[@"utdid"] = [UIDevice imy_utdidID];
        infoDict[@"device_id"] = [UIDevice imy_macaddress];
        
    }
    ///版本参数
    {
        infoDict[@"osversion"] = [IMYSystem stringVersion];
        infoDict[@"client"] = APPVersion;
        if (NSBundle.enableMYAppInfo) {
            infoDict[@"v"] = [NSBundle olderApp3rdVersion];
            infoDict[@"v1"] = APPVersion;
        } else {
            infoDict[@"v"] = APPVersion;
        }
        infoDict[@"clientversion"] = APPBuildVersion;
        infoDict[@"channelid"] = appHelper.channelID;
    }
    // 网络参数
    infoDict[@"apn"] = [IMYNetState apn];
    
    // 订阅权限信息
    const NSInteger currentRightsType = [IMYRightsSDK sharedInstance].currentRightsType;
    if (currentRightsType > 0) {
        infoDict[@"vip_type"] = @(currentRightsType);
    }
    
    // 页面路径
    infoDict[@"source"] = [self currentPageSource];

    NSString *userid = appHelper.userid;
    if (userid.imy_isPositiveInt) {
        infoDict[@"userid"] = userid;
        infoDict[@"uid"] = userid;
    }
    
    // 用户年龄
    infoDict[@"age"] = @(appHelper.userAge);
    
    // 计算AppIcon类型
    NSInteger const appIconType = IMYHIVE_BINDER(IOCAppInfo).currentAppIconType;
    NSInteger biIconTag = 1; // 普通版
    if (appIconType == 1) {
        biIconTag = 3;  // 亲友版
    } else if (appIconType >= 2) {
        biIconTag = 2; // VIP
    }
    // 极简模式、青少年模式、会员状态、样式ID、AppIcon类型、AppIcon具体值、最新会员类型
    NSString *bi_model = [NSString stringWithFormat:@"%ld%ld%.2ld%ld%ld%.3ld%.2ld",
                          MAX(0, MIN(9, IMYHIVE_BINDER(IOCAppInfo).currentAppPattern == 2 ? 2 : 1)),
                          MAX(0, MIN(9, appHelper.useYoungMode ? 2 : 1)),
                          MAX(0, MIN(99, IMYRightsSDK.sharedInstance.currentSubscribeType)),
                          MAX(0, MIN(9, IMYHIVE_BINDER(IOCAppInfo).currentNewThemeID)),
                          MAX(0, MIN(9, biIconTag)),
                          MAX(0, MIN(999, appIconType)),
                          MAX(0, MIN(99, IMYRightsSDK.sharedInstance.detailModel.latest_type))];
    
    infoDict[@"bi_model"] = bi_model;
    
    ///设备DNA
    NSString *dna = [UIDevice imy_DNA];
    if (dna.length > 0) {
        infoDict[@"dna"] = dna;
    }
    
    // idfv
    infoDict[@"idfv"] = [UIDevice imy_idfv2String];
    
    // 广告需求，增加4个参数 idfv、nop、country、language
    // TAPD: https://www.tapd.cn/38419447/prong/stories/view/1138419447001086742
    if (isAD) {
        [self setupADStatInfo:infoDict];
    }
    
    return infoDict;
}

+ (NSDictionary *)old_statInfoHeadersWithUrlString:(NSString * const)urlString
                                        apiHeaders:(NSDictionary * const)apiHeaders {
    BOOL isAD = [urlString containsString:@"/getad"];
    NSMutableDictionary *infoDict = [self old_statInfoMapWithAD:isAD];
    // run hooks
    for (NSDictionary * (^hook)(NSString *, NSDictionary *) in [self sharedInstance].statinfoHooks) {
        NSDictionary *map = hook(urlString, apiHeaders);
        if (map.count > 0) {
            [infoDict addEntriesFromDictionary:map];
        }
    }
    return [self statInfoEncryptHeadersForInfoDict:infoDict urlHost:urlString.imy_host];
}

+ (BOOL)usingOnceADInfo {
    return YES;
}

+ (NSDictionary *)getOnceADInfoMap {
    NSDictionary *map = @{
        // 系统启动时间
        @"boot" : @([UIDevice imy_systemBootTime]),
        // 系统更新时间
        @"sysup" : @([UIDevice imy_systemUpdateTime]),
        // 设备名
        @"hwmodel" : [UIDevice imy_systemHardware],
        // 系统更新时间
        @"update_mark" : [UIDevice imy_systemUpdateMark],
        // 设备初始化时间
        @"birth_time" : [UIDevice imy_birthTime],
        // 手机磁盘空间
        @"disk" : @(imy_diskTotalCapacity()),
#if __has_include(<IMYDynamicFrameworks/WXApi.h>)
        @"opensdk_ver" : [WXApi getApiVersion],
#endif
        @"c_disk" : [UIDevice caid_disk] ?: @"",
        @"c_sysup" : [UIDevice caid_sysFileTime] ?: @"",
        @"c_mntid" : [UIDevice caid_mntId] ?: @"",
    };
    return map;
}

+ (void)setupADStatInfo:(NSMutableDictionary *)infoDict {
    // 广告需求，增加4个参数 idfv、nop、country、language
    // TAPD: https://www.tapd.cn/38419447/prong/stories/view/1138419447001086742
    infoDict[@"nop"] = [UIDevice imy_carrierCode] ?: @"";
    infoDict[@"country"] = [UIDevice imy_countryCode];
    infoDict[@"language"] = [UIDevice imy_language];
    
    // 广告需求，增加6个参数 boot、sysup、tz、dname、mem、disk
    // TAPD: https://www.tapd.cn/34134977/prong/stories/view/1134134977001088549
    
    // 广告静态参数，可以不用每次都实时获取
    NSDictionary *adInfoMap = nil;
    if ([self usingOnceADInfo]) {
        static NSDictionary *onceInfo;
        static dispatch_once_t onceToken;
        dispatch_once(&onceToken, ^{
            onceInfo = [self getOnceADInfoMap];
        });
        adInfoMap = onceInfo;
    } else {
        adInfoMap = [self getOnceADInfoMap];
    }
    [infoDict addEntriesFromDictionary:adInfoMap];
    
    infoDict[@"tz"] = @(NSTimeZone.systemTimeZone.secondsFromGMT);
    infoDict[@"dname"] = UIDevice.currentDevice.name.imy_md5;
    infoDict[@"mem"] = @([UIDevice imy_physicalMemory]);
    //广告需求，新增anip参数(所有活跃网卡有效的IPv6地址）
    //TAPD: https://www.tapd.cn/38419447/prong/stories/view/1138419447001137271
    NSDictionary *allIpV6 = [UIDevice imy_getAllIPV6];
    if (allIpV6) {
        //广告接口要求用string方式
        NSString *allIpV6Str = [allIpV6 imy_jsonString];
        infoDict[@"anip"] = allIpV6Str;
    }
#if __has_include(<IMYDynamicFrameworks/WXApi.h>)
    infoDict[@"wx_installed"] = @([WXApi isWXAppInstalled]);
#endif
    infoDict[@"c_ot"] = [UIDevice caid_carrierInfo];
}

+ (NSMutableDictionary *)v2_statInfoMapWithAD:(const BOOL)isAD {
    NSMutableDictionary *infoDict = [NSMutableDictionary dictionaryWithCapacity:64];
    IMYPublicAppHelper *appHelper = [IMYPublicAppHelper shareAppHelper];
    
    infoDict[@"ot"] = [UIDevice imy_carrierName];
    infoDict[@"ua"] = [UIDevice imy_platform];
    infoDict[@"webua"] = [[UIDevice class] imy_userAgent];
    
    if ([[UIDevice imy_machineModel] containsString:@"iPad"]) {
        infoDict[@"os"] = @(5);
    } else {
        infoDict[@"os"] = @(1);
    }

    infoDict[@"osversion"] = [IMYSystem stringVersion];
    infoDict[@"openudid"] = [UIDevice imy_openUDID];
    infoDict[@"umengid"] = [UIDevice imy_umengID];
    infoDict[@"utdid"] = [UIDevice imy_utdidID];
    infoDict[@"device_id"] = [UIDevice imy_macaddress];
    
    // 网络参数
    infoDict[@"apn"] = [IMYNetState apn];
    
    // 订阅权限信息
    const NSInteger currentRightsType = [IMYRightsSDK sharedInstance].currentRightsType;
    if (currentRightsType > 0) {
        infoDict[@"vip_type"] = @(currentRightsType);
    }
    
    // 用户uid
    NSString *userid = appHelper.userid;
    if (userid.imy_isPositiveInt) {
        infoDict[@"uid"] = userid;
    }
    
    // 用户年龄
    infoDict[@"age"] = @(appHelper.userAge);

    // 计算AppIcon类型
    NSInteger const appIconType = IMYHIVE_BINDER(IOCAppInfo).currentAppIconType;
    NSInteger biIconTag = 1; // 普通版
    if (appIconType == 1) {
        biIconTag = 3;  // 亲友版
    } else if (appIconType >= 2) {
        biIconTag = 2; // VIP
    }
    // 极简模式、青少年模式、会员状态、样式ID、AppIcon类型、AppIcon具体值、最新会员类型
    NSString *bi_model = [NSString stringWithFormat:@"%ld%ld%.2ld%ld%ld%.3ld%.2ld",
                          MAX(0, MIN(9, IMYHIVE_BINDER(IOCAppInfo).currentAppPattern == 2 ? 2 : 1)),
                          MAX(0, MIN(9, appHelper.useYoungMode ? 2 : 1)),
                          MAX(0, MIN(99, IMYRightsSDK.sharedInstance.currentSubscribeType)),
                          MAX(0, MIN(9, IMYHIVE_BINDER(IOCAppInfo).currentNewThemeID)),
                          MAX(0, MIN(9, biIconTag)),
                          MAX(0, MIN(999, appIconType)),
                          MAX(0, MIN(99, IMYRightsSDK.sharedInstance.detailModel.latest_type))];
    
    infoDict[@"bi_model"] = bi_model;
    
    infoDict[@"buildv"] = APPBuildVersion;
    
    infoDict[@"idfa"] = [UIDevice imy_idfaString] ?: @"";
    infoDict[@"aaid"] = [UIDevice imy_AAID];
    infoDict[@"caid"] = [IMYHIVE_BINDER(IOCCAIDManager) getCacheCAID] ?: @"";
    
    // 设备DNA
    NSString *dna = [UIDevice imy_DNA];
    if (dna.length > 0) {
        infoDict[@"dna"] = dna;
    }
    
    // idfv
    infoDict[@"idfv"] = [UIDevice imy_idfv2String];
    
    // 广告需求，增加4个参数 idfv、nop、country、language
    // TAPD: https://www.tapd.cn/38419447/prong/stories/view/1138419447001086742
    if (isAD) {
        [self setupADStatInfo:infoDict];
    }
    
    return infoDict;
}

+ (NSDictionary *)v2_statInfoHeadersWithUrlString:(NSString * const)urlString
                                       apiHeaders:(NSDictionary * const)apiHeaders {
    BOOL isAD = [urlString containsString:@"/getad"];
    NSMutableDictionary *infoDict = [self v2_statInfoMapWithAD:isAD];
    // run hooks
    for (NSDictionary * (^hook)(NSString *, NSDictionary *) in [self sharedInstance].statinfoHooks) {
        NSDictionary *map = hook(urlString, apiHeaders);
        if (map.count > 0) {
            [infoDict addEntriesFromDictionary:map];
        }
    }
    return [self statInfoEncryptHeadersForInfoDict:infoDict urlHost:urlString.imy_host];
}

+ (BOOL)isMeiYouSite:(NSString *)urlString {
    if (!urlString) {
        return NO;
    }
    if ([urlString hasPrefix:@"file://"]) {
        return YES;
    }
    NSString *hostString = urlString;
    {
        NSRange schemeRange = [hostString rangeOfString:@"://"];
        if (schemeRange.location != NSNotFound) {
            NSUInteger beginIndex = schemeRange.location + schemeRange.length;
            hostString = [hostString substringFromIndex:beginIndex];
        }
        static NSCharacterSet *characterSet;
        static dispatch_once_t onceToken;
        dispatch_once(&onceToken, ^{
            characterSet = [NSCharacterSet characterSetWithCharactersInString:@"/?"];
        });
        NSRange hostRange = [hostString rangeOfCharacterFromSet:characterSet options:0];
        if (hostRange.location != NSNotFound) {
            hostString = [hostString substringToIndex:hostRange.location];
        }
    }
    NSArray *myHosts = [self myHosts];
    for (NSString *obj in myHosts) {
        if ([hostString containsString:obj]) {
            return YES;
        }
    }
    return NO;
}

static NSArray *_meetYouHosts = nil;
+ (NSArray *)myHosts {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _meetYouHosts = @[
            @"seeyouyima.com",
            @".meiyou.com",
            @".xmmeiyou.com",
            @".xmseeyouyima.com",
            @".youzibuy.com",
            @".xmyouzijia.com",
            @".seeyouhealth.com",
            @".listenvod.com",
            @".tianya.tv",
            @".fanhuan.com",
            @".tataquan.com",
            @".xixiaoyou.com",
            @".meetyouhuodong.com",
            @"192.168.",
            @"10.0.",
        ];
    });
    NSArray *myHosts = _meetYouHosts;
    return myHosts;
}

+ (void)addMeetYouHosts:(NSArray *)hosts {
    NSArray *temp = [self myHosts];
    NSMutableArray *myHosts = [NSMutableArray arrayWithArray:temp];
    BOOL hasChanged = NO;
    for (NSString *addHost in hosts) {
        if (![myHosts containsObject:addHost]) {
            [myHosts addObject:addHost];
            hasChanged = YES;
        }
    }
    if (hasChanged) {
        _meetYouHosts = [myHosts copy];
        imy_asyncMainBlock(2, ^{
            [temp class];
        });
    }
}

@end


@implementation IMYMeetyouHTTPHooks (Hooks)

IMY_KYLIN_FUNC_PREMAIN {
//    [IMYWebImageManager setHTTPSDNS:[IMYHTTPDNS shareInstance]];
//    [IMYHTTPSettings setHTTPSDNS:[IMYHTTPDNS shareInstance]];
    
    // 图片开始请求
    [[NSNotificationCenter defaultCenter] addObserverForName:IMYWebImageWillDownloadStartNotification
                                                      object:nil
                                                       queue:nil
                                                  usingBlock:^(NSNotification *_Nonnull note) {
        NSMutableURLRequest *request = note.object;
        [IMYMeetyouHTTPHooks willRequestHook:request];
    }];
    
    // 图片下载失败
    [[NSNotificationCenter defaultCenter] addObserverForName:SDWebImageDownloadErrorNotification
                                                      object:nil
                                                       queue:nil
                                                  usingBlock:^(NSNotification * _Nonnull notification) {
        NSDictionary *dict = notification.userInfo;
        CFAbsoluteTime requestTime = [dict[@"diffTime"] doubleValue];
        NSError *error = dict[@"error"];
        NSURL *failingURL = dict[@"url"];
        if (![failingURL isKindOfClass:NSURL.class]) {
            failingURL = nil;
        }
        NSData *responseData = dict[@"data"];
        if (![responseData isKindOfClass:NSData.class]) {
            responseData = nil;
        }
        [IMYMeetyouHTTPHooks checkNeedReportInfoWithError:error
                                               failingURL:failingURL
                                             responseData:responseData
                                              requestTime:requestTime];
    }];
}

+ (NSMutableDictionary *)headersWithUrlString:(NSString * const)urlString
                                      headers:(NSDictionary * const)headers {
    NSMutableDictionary *addHeaders = [self addtionalHeadersWithUrlString:urlString apiHeaders:headers];

    if ([self isMeiYouSite:urlString]) {
        IMYPublicAppHelper *appHelper = [IMYPublicAppHelper shareAppHelper];
        BOOL needAddAuth = imy_isNotBlankString(appHelper.userToken);
        ///未登录 但是有虚拟授权
        if (!needAddAuth && imy_isNotBlankString(appHelper.virtualToken) && imy_isNotBlankString(appHelper.userid)) {
            needAddAuth = YES;
        }
        ///登录接口不需要加 授权
        if (needAddAuth && ([urlString containsString:@"userlogin?sign="] || [urlString containsString:@"reg?sign="])) {
            needAddAuth = NO;
        }
        ///注册接口不用加
        if (needAddAuth && [urlString containsString:@"logins?sign="]) {
            needAddAuth = NO;
        }
        /// 新版登录接口
        if (needAddAuth && [urlString containsString:@"v2/logins"] && [headers[@"regsign"] length] > 0) {
            needAddAuth = NO;
        }
        
        ///如果已有授权就不加
        NSString *authField = headers[@"Authorization"];
        if (needAddAuth && imy_isNotBlankString(authField)) {
            needAddAuth = NO;
        }
        ///请求访客用户授权接口 不加
        if (needAddAuth && [urlString containsString:@"nologin"]) {
            needAddAuth = NO;
        }
        ///不需要添加用户token
        if (!needAddAuth) {
            [addHeaders removeObjectForKey:@"Authorization"];
        }
        ///已有虚拟授权 就不添加
        NSString *vAuthField = headers[@"Authorization-Virtual"];
        if (imy_isNotBlankString(vAuthField)) {
            [addHeaders removeObjectForKey:@"Authorization-Virtual"];
        }
    }
    // run hooks
    for (NSDictionary *(^hook)(NSString *) in [self sharedInstance].headersHooks) {
        NSDictionary *map = hook(urlString);
        if (map.count > 0) {
            [addHeaders addEntriesFromDictionary:map];
        }
    }
    
    /// https://www.tapd.meiyou.com/21039721/prong/stories/view/1121039721001162759
    /// 带移除关键字，则不追加 statinfo
    if ([urlString containsString:@"nostatinfo=1"]) {
        [addHeaders removeObjectForKey:@"statinfo"];
        [addHeaders removeObjectForKey:@"sv"];
    }
    
    return addHeaders;
}

+ (NSMutableDictionary *)queriesWithUrlString:(NSString *)urlString {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (!urlString.length) {
        return params;
    }
    NSURL *url = [NSURL imy_URLWithString:urlString];
    if (!url) {
        return params;
    }
    if ([self isMeiYouSite:url.host] && ![self isV2URLWithHost:url.host andPath:url.path]) {
        // 旧版才加（V2头部已包含以下信息）
        IMYPublicAppHelper *appHelper = [IMYPublicAppHelper shareAppHelper];
        if (NSBundle.enableMYAppInfo) {
            params[@"v"] = [NSBundle olderApp3rdVersion];
            params[@"v1"] = APPVersion;
        } else {
            params[@"v"] = APPVersion;
        }
        params[@"platform"] = @"ios";
        params[@"lang"] = appHelper.language;
        if (IMYHIVE_BINDER(IOCAppInfo).clang) {
            params[@"clang"] = IMYHIVE_BINDER(IOCAppInfo).clang;
        }
        
        params[@"themeid"] = appHelper.themeID;
        params[@"channelID"] = [appHelper.channelID imy_URLEncodedString];
        params[@"app_id"] = appHelper.app_id;
        params[@"scale"] = [NSString stringWithFormat:@"%.1f", SCREEN_SCALE];
        if (![url.query containsString:@"&mode="] && ![url.query hasPrefix:@"mode="]) {
            params[@"mode"] = [NSString stringWithFormat:@"%ld", (long)appHelper.userMode];
        }
    }
    // run hooks
    for (NSDictionary *(^hook)(NSString *) in [self sharedInstance].queriesHooks) {
        NSDictionary *map = hook(urlString);
        if (map.count > 0) {
            [params addEntriesFromDictionary:map];
        }
    }
    return params;
}

+ (BOOL)gzipEncodingWithUrlString:(NSString *)urlString {
    NSString *urlHost = [NSURL imy_URLWithString:urlString].host;
    NSArray *list = [self sharedInstance].gzipHosts;
    BOOL gzipEncoding = NO;
    for (NSString *host in list) {
        if ([urlHost isEqualToString:host]) {
            gzipEncoding = YES;
            break;
        }
    }
    return gzipEncoding;
}

+ (void)willRequestHook:(NSMutableURLRequest *)request {
    NSURL *URL = [[IMYPublicURLManager sharedManager] switchHostWithURL:request.URL];
    if (request.URL != URL) {
        request.URL = URL;
    }
    [IMYHTTPDNS dnsInstallWithRequest:request];

    for (void (^hook)(NSMutableURLRequest *) in [self sharedInstance].willRequestHooks) {
        hook(request);
    }
}

+ (void)didFailHook:(NSError *)error requestTime:(const NSTimeInterval)requestTime {
    NSString *failingUrl = error.ns_failingURL.absoluteString;
    if ([IMYMeetyouHTTPHooks isMeiYouSite:failingUrl]) {
        // 检测auth是否过期
        [self checkAuthInvalidWithFail:error];
        // 检测是否需要二次验证
        [self checkIdentityValidWithFail:error];
    }

    [self logNetworkErrorWithError:error requestTime:requestTime];

    for (void (^hook)(NSError *) in [self sharedInstance].didFailHooks) {
        hook(error);
    }
    // 上报大前端错误平台
    [self checkNeedReportInfoWithError:error
                            failingURL:error.ns_failingURL
                          responseData:error.af_responseData
                           requestTime:requestTime];
}

+ (void)checkNeedReportInfoWithError:(NSError * const)error
                          failingURL:(NSURL * const)failingURL
                        responseData:(NSData * const)responseData
                         requestTime:(NSTimeInterval const)requestTime {
    // 无网络，直接返回
    if (!IMYNetState.networkEnable) {
        return;
    }
    // 接口成功 或者 用户取消，不进行上报
    if ((error.code == 200 || error.code == -999) && requestTime < 10) {
        return;
    }
    
    // 无法获取 host/path
    NSString * const hostPath = failingURL.imy_onlyHostPath;
    if (!hostPath.length) {
        return;
    }
    
    // 执行域名路由监测 (网络引起的错误)
    if ([IMYHTTPBackupHostConfig isNetworkingTypeError:error]) {
        [self checkNeedTracerouteWithFailingURL:failingURL];
    }
    
    // 上报通用接口错误
    NSArray<NSDictionary *> *reportMaps = [[IMYConfigsCenter sharedInstance] arrayForKeyPath:@"meetyou_app_setting.abnormal_request_reporting.urls"];
    BOOL needReport = NO;
    for (NSDictionary *map in reportMaps) {
        NSString * const hitUrl = map[@"url"];
        if (!hitUrl.length) {
            continue;
        }
        if ([hostPath containsString:hitUrl]) {
            needReport = YES;
            break;
        }
        // 备用域名判断
        NSString * const origHost = [hitUrl componentsSeparatedByString:@"/"].firstObject;
        NSString * const backupHost = [IMYHTTPBackupHostConfig getBackupHostConfigWithHost:origHost].backup;
        if (!backupHost.length) {
            continue;
        }
        NSString * const backupHit = [hitUrl stringByReplacingOccurrencesOfString:origHost withString:backupHost];
        if ([hostPath containsString:backupHit]) {
            needReport = YES;
            break;
        }
    }
    if (!needReport) {
        return;
    }
    
    NSMutableDictionary *detail = [NSMutableDictionary dictionary];
    detail[@"url"] = failingURL.absoluteString;
    detail[@"code"] = @(error.code);
    detail[@"reason"] = error.localizedFailureReason ?: error.localizedDescription;
    if (responseData.length < 2048) {
        // 小于2kb的数据才上报
        detail[@"errbody"] = [responseData imy_utf8String] ?: @"";
    } else {
        detail[@"errbody"] = [NSString stringWithFormat:@"size:%ld", responseData.length];
    }
    detail[@"time"] = [NSString stringWithFormat:@"%.2lf", requestTime];
    // 上报大前端告警平台
    [IMYErrorTraces postWithType:202 pageName:self.currentPageName category:IMYErrorTraceCategoryAPICommon message:hostPath detail:detail.imy_jsonString];
}

+ (void)checkNeedTracerouteWithFailingURL:(NSURL * const)failingURL {
    NSArray * const list = [[IMYConfigsCenter sharedInstance] arrayForKeyPath:@"apptech.auto_traceroute.err_list"];
    if (!list.count) {
        return; // 无需进行域名路由检测
    }
    
    // 无法获取 host
    NSString * const urlHost = failingURL.host;
    if (!urlHost.length) {
        return;
    }
    
    static NSMutableDictionary *kCheckMap = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        kCheckMap = [NSMutableDictionary dictionary];
    });
    
    for (NSDictionary *map in list) {
        NSString * const host = map[@"host"];
        NSInteger const count = [map[@"count"] integerValue];
        
        if (!host.length || count == 0) {
            continue; // 没有host或者无次数限制
        }
        if (![urlHost containsString:host]) {
            continue; // 不包含对应的关键字
        }
        
        // 判断一分钟的错误次数是否超额
        @synchronized (kCheckMap) {
            NSInteger const hitNum = [kCheckMap[urlHost] integerValue] + 1;
            kCheckMap[urlHost] = @(hitNum);
            
            if (hitNum >= count) {
                // 执行网络检测
                [IMYErrorTraces postWithNetCheckHost:urlHost
                                            category:IMYErrorTraceCategoryAPICommon
                                             message:@"debug_net_traceroute"];
            }
        }
        
        // 1分钟后减一
        imy_asyncMainBlock(60, ^{
            @synchronized (kCheckMap) {
                NSInteger const hitNum = [kCheckMap[urlHost] integerValue] - 1;
                kCheckMap[urlHost] = @(MAX(0, hitNum));
            }
        });
    }
}

// 判断是否需要进行 身份校验
+ (void)checkIdentityValidWithFail:(NSError *)error {
    const NSInteger httpCode = error.af_httpResponse.statusCode;
    // 只有 http code = 403 的情况下，才判断对应错误code
    if (httpCode != 403) {
        return;
    }
    NSData *responseData = error.af_responseData;
    NSDictionary *errorMap = [NSData imy_dictionaryWithJSONData:responseData];
    NSInteger errorCode = [[errorMap objectForKey:@"code"] integerValue];
    if (errorCode == 10000113) {
        NSString *uriAction = errorMap[@"data"][@"uri"];
        imy_asyncMainBlock(0.5, ^{
            // 在主线程中 执行URI跳转，延迟0.5秒是应对PresentVC收起后，验证页面也被收起的问题（帖子发布）
            [[IMYURIManager shareURIManager] runActionWithString:uriAction];
        });
    }
}

/// 本地token 跟 接口传递的token不一致，则不执行后续逻辑
+ (BOOL)checkRequestTokenSameLocal:(NSError *)error {
    // 判断请求token 跟 本地正式token 一致
    NSString * const userToken = [IMYPublicAppHelper shareAppHelper].userToken;
    NSString * const afRToken = error.imyaf_userToken;
    if (userToken.length > 0 || afRToken.length > 0) {
        return userToken && [afRToken hasSuffix:userToken];
    }
    
    // 判断请求token 跟 本地虚拟token 一致
    NSString * const virtualToken = [IMYPublicAppHelper shareAppHelper].virtualToken;
    NSString * const afVToken = error.imyaf_virtualToken;
    if (virtualToken.length > 0 || afVToken.length > 0) {
        return virtualToken && [afVToken hasSuffix:virtualToken];
    }
    
    // 验证不通过
    return NO;
}

// 判断auth是否失效
+ (void)checkAuthInvalidWithFail:(NSError *)error {
    const NSInteger httpCode = error.af_httpResponse.statusCode;
    // 只有 http code = 401 的情况下，才判断对应错误code
    if (httpCode != 401) {
        return;
    }
    
    // 校验请求token是否跟本地一致
    if (![self checkRequestTokenSameLocal:error]) {
        return;
    }
    
    NSData *responseData = error.af_responseData;
    NSDictionary *errorMap = [NSData imy_dictionaryWithJSONData:responseData];
    NSInteger errorCode = [[errorMap objectForKey:@"code"] integerValue];
    switch (errorCode) {
        case 1:
        case 4:
        case 5:
        case 6:
        case 13: {
            imy_asyncMainExecuteBlock(^{
                [[NSNotificationCenter defaultCenter] postNotificationName:[self userTokenInvalidNotification]
                                                                    object:error
                                                                  userInfo:nil];
            });
        } break;
        case 7: {
            imy_asyncMainExecuteBlock(^{
                [[NSNotificationCenter defaultCenter] postNotificationName:[self phoneInvalidNotification]
                                                                    object:error
                                                                  userInfo:nil];
            });
        } break;
            
        case 11:
        case 16: {
            NSString *message = [errorMap objectForKey:@"message"];
            imy_asyncMainExecuteBlock(^{
                [UIAlertView imy_quickAlert:message];
            });
        } break;
        case 21: {
            imy_asyncMainExecuteBlock(^{
                [[IMYURIManager shareURIManager] runActionWithString:@"bind/phone"];
            });
        } break;
        case 10000116: {
            //微信解除授权后统一返回code: 10000116
            imy_asyncMainExecuteBlock(^{
                [IMYPublicAppHelper shareAppHelper].isThirdAutoCancel = YES;
                static BOOL hasPost = NO;
                if (hasPost == NO) {
                    hasPost = YES;
                    [IMYGAEventHelper postWithPath:@"event" params:@{@"action":@1, @"event":@"bbj_wxqxsq_tcdltc"} headers:nil completed:nil];
                    // 保存弹窗状态，供弹窗优先级使用
                    IMYWechatLogoutAlertShowing = YES;
                }
                [UIAlertController imy_showAlertViewWithTitle:IMYString(@"提示") message:IMYString(@"微信登录已下线，请重新登录") cancelButtonTitle:IMYString(@"确定") otherButtonTitles:nil handler:^(UIAlertController *alertController, NSInteger buttonIndex) {
                    IMYWechatLogoutAlertShowing = NO;
                    
                    [IMYGAEventHelper postWithPath:@"event" params:@{@"action":@2, @"event":@"bbj_wxqxsq_tcdltc"} headers:nil completed:nil];
                    hasPost = NO;
                    [IMYPublicAppHelper shareAppHelper].isThirdAutoCancel = NO;
                    //kAPI_weiXinCancelAuth
                    [[[IMYServerRequest postPath:@"v3/wechat_revoke_clear" host:users_seeyouyima_com params:nil headers:nil] deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
                        [[IMYURIManager shareURIManager] runActionWithPath:@"user/loginout_v2" params:nil info:nil];
                    } error:^(NSError * _Nullable error) {
                        [UIWindow imy_showTextHUD:error.localizedDescription];
                    }];
                }];
            });
        } break;
            
#ifdef DEBUG
        case 8: {
            ///测试环境中弹窗， auth 跟 uid 不对的情况
            NSString *message = [errorMap objectForKey:@"message"];
            imy_asyncMainExecuteBlock(^{
                [UIAlertView imy_quickAlert:message];
            });
        } break;
#endif
        default:
            break;
    }
}

+ (void)didSuccessHook:(IMYHTTPResponse *)response requestTime:(const NSTimeInterval)requestTime {
    [self logNetworkSuccessWithResponse:response requestTime:requestTime];

    for (void (^hook)(IMYHTTPResponse *) in [self sharedInstance].didSuccessHooks) {
        hook(response);
    }
}

+ (NSSet<NSString *> *)acceptableContentTypesHook:(IMYHTTPSerializerType)type {
    //NOTE: 兼容下服务端，有的接口返回 JSON，可 content-type 却没有设置
    if (IMYHTTPSerializerTypeJSON == type) {
        return [NSSet setWithObjects:@"text/plain", @"text/html", nil];
    } else {
        return nil;
    }
}

/// add hooks
+ (void)addDidFailHook:(void (^)(NSError *))didFailHook forKey:(NSString *)key {
    NSParameterAssert(didFailHook);
    NSParameterAssert(key);
    NSParameterAssert(NSThread.isMainThread);
    NSParameterAssert(imy_load_current() == IMYLoadAtPreMain);
    IMYMeetyouHTTPHooks *hooks = [self sharedInstance];
    [hooks.didFailHookMaps setObject:[didFailHook copy] forKey:key];
    hooks.didFailHooks = hooks.didFailHookMaps.allValues;
}

+ (void)addDidSuccessHook:(void (^)(IMYHTTPResponse *))didSuccessHook forKey:(NSString *)key {
    NSParameterAssert(didSuccessHook);
    NSParameterAssert(key);
    NSParameterAssert(NSThread.isMainThread);
    NSParameterAssert(imy_load_current() == IMYLoadAtPreMain);
    IMYMeetyouHTTPHooks *hooks = [self sharedInstance];
    [hooks.didSuccessHookMaps setObject:[didSuccessHook copy] forKey:key];
    hooks.didSuccessHooks = hooks.didSuccessHookMaps.allValues;
}

+ (void)addQueriesHook:(NSDictionary * (^)(NSString *urlString))quriesHook forKey:(NSString *)key {
    NSParameterAssert(quriesHook);
    NSParameterAssert(key);
    NSParameterAssert(NSThread.isMainThread);
    NSParameterAssert(imy_load_current() == IMYLoadAtPreMain);
    IMYMeetyouHTTPHooks *hooks = [self sharedInstance];
    [hooks.queriesHookMaps setObject:[quriesHook copy] forKey:key];
    hooks.queriesHooks = hooks.queriesHookMaps.allValues;
}

+ (void)addStatInfoHook:(NSDictionary *(^)(NSString *, NSDictionary *))statinfoHook forKey:(NSString *)key {
    NSParameterAssert(statinfoHook);
    NSParameterAssert(key);
    NSParameterAssert(NSThread.isMainThread);
    NSParameterAssert(imy_load_current() == IMYLoadAtPreMain);
    IMYMeetyouHTTPHooks *hooks = [self sharedInstance];
    [hooks.statinfoHookMaps setObject:[statinfoHook copy] forKey:key];
    hooks.statinfoHooks = hooks.statinfoHookMaps.allValues;
}

+ (void)addHeadersHook:(NSDictionary * (^)(NSString *urlString))headersHook forKey:(NSString *)key {
    NSParameterAssert(headersHook);
    NSParameterAssert(key);
    NSParameterAssert(NSThread.isMainThread);
    NSParameterAssert(imy_load_current() == IMYLoadAtPreMain);
    IMYMeetyouHTTPHooks *hooks = [self sharedInstance];
    [hooks.headersHookMaps setObject:[headersHook copy] forKey:key];
    hooks.headersHooks = hooks.headersHookMaps.allValues;
}

+ (void)addWillRequestHook:(void (^)(NSMutableURLRequest *))willRequestHook forKey:(NSString *)key {
    NSParameterAssert(willRequestHook);
    NSParameterAssert(key);
    NSParameterAssert(NSThread.isMainThread);
    NSParameterAssert(imy_load_current() == IMYLoadAtPreMain);
    IMYMeetyouHTTPHooks *hooks = [self sharedInstance];
    [hooks.willRequestHookMaps setObject:[willRequestHook copy] forKey:key];
    hooks.willRequestHooks = hooks.willRequestHookMaps.allValues;
}

@end


@implementation IMYMeetyouHTTPHooks (Compatibility)

+ (BOOL)ignoreAppendingHeadersWithRequest:(NSURLRequest *)request {
    if (request.URL.isFileURL) {
        // 文件路径，不加请求头
        return YES;
    }
    return NO;
}

+ (void)addRequestHeadersToRequest:(NSURLRequest *)urlRequest {
    if ([self ignoreAppendingHeadersWithRequest:urlRequest]) {
        // 无需追加请求头
        return;
    }
    NSMutableURLRequest *mutableRequest = [urlRequest imy_mutableConverted];
    NSString *urlString = mutableRequest.URL.absoluteString;
    NSDictionary *headers = mutableRequest.allHTTPHeaderFields;
    NSDictionary *appendHeaders = [IMYMeetyouHTTPHooks headersWithUrlString:urlString headers:headers];
    if (appendHeaders.count > 0) {
        NSMutableDictionary *newAllHeaders = [NSMutableDictionary dictionaryWithDictionary:headers];
        [newAllHeaders addEntriesFromDictionary:appendHeaders];
        [mutableRequest setAllHTTPHeaderFields:newAllHeaders];
    }
}

@end

@implementation IMYMeetyouHTTPHooks (Deprecated)

+ (void)addDidFailHook:(void (^)(NSError *))didFailHook {
    NSParameterAssert(didFailHook);
    static NSInteger i;
    [self addDidFailHook:didFailHook forKey:[NSString stringWithFormat:@"__deprecated-%ld", ++i]];
}

+ (void)addDidSuccessHook:(void (^)(IMYHTTPResponse *))didSuccessHook {
    NSParameterAssert(didSuccessHook);
    static NSInteger i;
    [self addDidSuccessHook:didSuccessHook forKey:[NSString stringWithFormat:@"__deprecated-%ld", ++i]];
}

+ (void)addQueriesHook:(NSDictionary * (^)(NSString *urlString))quriesHook {
    NSParameterAssert(quriesHook);
    static NSInteger i;
    [self addQueriesHook:quriesHook forKey:[NSString stringWithFormat:@"__deprecated-%ld", ++i]];
}

+ (void)addHeadersHook:(NSDictionary * (^)(NSString *urlString))headersHook {
    NSParameterAssert(headersHook);
    static NSInteger i;
    [self addHeadersHook:headersHook forKey:[NSString stringWithFormat:@"__deprecated-%ld", ++i]];
}

+ (void)addWillRequestHook:(void (^)(NSMutableURLRequest *))willRequestHook {
    NSParameterAssert(willRequestHook);
    static NSInteger i;
    [self addWillRequestHook:willRequestHook forKey:[NSString stringWithFormat:@"__deprecated-%ld", ++i]];
}

#ifdef DEBUG

/// 解密statinfo请求头
+ (NSDictionary *)debug_statInfoDecryptHeadersForInfoDict:(NSDictionary *)infoDict {
    
    NSString *base64String = infoDict[@"statinfo"];
    NSString *infoVer = infoDict[@"sv"];
    
    IMYCConfigsGroup * const ccGroup = [[IMYConfigsCenter sharedInstance] groupForKeyPath:@"meetyou_app_setting.st_config"];
    NSString *ccVer = [ccGroup stringForKey:@"v"];
    if (![ccVer isEqualToString:infoVer]) {
        return nil;
    }
    
    NSString *aes256Key = [[ccGroup stringForKey:@"key"] imy_reversedString];
    NSString *aesIV = @"AST378JKD98FGTRr";
    NSData *encryptData = [base64String base64DecodedData];
    
    NSData *infoJsonData = [NSData imy_decryptedData:encryptData aes256Key:aes256Key iv:aesIV];
    NSDictionary *jsonObject = [infoJsonData imy_jsonObject];
    NSLog(@"%@", jsonObject);
    return jsonObject;
}

+ (void)debug_statInfoDecrypt {
    [self debug_statInfoDecryptHeadersForInfoDict:@{
        @"statinfo" : @"A9c8WZwOOvq9dbgbrGvijZtOPhU8XqcQQLWEY7FPA6B28zCgVmoNpgTV0jdKawIf86PLr1HkaYxKD48NY7DWPARPPDDQq1uF64SuRmFMIW2Gj3v3ruPKIPc+AqjRnt3A9z0k0fz6ep51+8Wep0HHyAz9iSRLp1AsJJqcCc2ARgQbINDXqksq4e2ys8qz6dw64/TOWhZFCICyf30ih4OjK0ApzborOX/zqsfPVt9xLDyVl7I7QDf3slAKumcBgGbTc4CFZxERNO96+t3RUNFVSA==",
        @"sv" : @"KK77",
    }];
}

IMY_KYLIN_FUNC_MAINTAB_ASYNC {
    [IMYMeetyouHTTPHooks debug_statInfoDecrypt];
}

#endif

#pragma mark - 网络性能监控辅助方法

+ (void)logNetworkErrorWithError:(NSError *)error requestTime:(NSTimeInterval)requestTime {
    IMYNetworkPerformanceMetrics *metrics = [[IMYNetworkPerformanceMetrics alloc] init];

    metrics.url = error.ns_failingURL.absoluteString ?: @"Unknown";
    metrics.method = @"Unknown";

    // 错误信息
    metrics.errorCode = [@(error.code) stringValue];
    metrics.errorMessage = error.localizedDescription;

    // 总耗时（从外部传入的requestTime）
    metrics.totalRequestDurationMs = requestTime * 1000; // 转换为毫秒

    // HTTP状态码
    if (error.af_httpResponse) {
        metrics.httpStatusCode = error.af_httpResponse.statusCode;
    }

    // 输出性能日志
    [IMYNetworkPerformanceMonitor logPerformanceMetrics:metrics];
}

+ (void)logNetworkSuccessWithResponse:(IMYHTTPResponse *)response requestTime:(NSTimeInterval)requestTime {
    IMYNetworkPerformanceMetrics *metrics = [[IMYNetworkPerformanceMetrics alloc] init];

    metrics.url = response.response.URL.absoluteString ?: @"Unknown";
    metrics.method = @"Unknown";
    metrics.httpStatusCode = response.response.statusCode;
    metrics.totalRequestDurationMs = requestTime * 1000;

    // 响应大小
    if (response.responseObject && [response.responseObject isKindOfClass:[NSData class]]) {
        NSData *responseData = (NSData *)response.responseObject;
        metrics.responseSizeBytes = responseData.length;
    } else {
        NSData *responseData = response.responseData;
        if (responseData) {
            metrics.responseSizeBytes = responseData.length;
        }
    }

    // 输出性能日志
    [IMYNetworkPerformanceMonitor logPerformanceMetrics:metrics];
}

+ (void)addNetworkPerformanceMonitoringHook {
    [self addWillRequestHook:^(NSMutableURLRequest *request) {
    } forKey:@"NetworkPerformanceMonitoring"];
}

+ (void)handleTaskMetrics:(NSURLSessionTaskMetrics *)metrics
                     task:(NSURLSessionTask *)task
                    error:(NSError *)error {
    IMYNetworkPerformanceMetrics *performanceMetrics =
        [IMYNetworkPerformanceMonitor extractMetricsFromTaskMetrics:metrics task:task error:error];
    [IMYNetworkPerformanceMonitor logPerformanceMetrics:performanceMetrics];
}
@end

#pragma mark - 全局 NSURLSession Hook 实现

@interface NSObject (IMYPerformanceMonitoring)
- (void)imy_URLSession:(NSURLSession *)session task:(NSURLSessionTask *)task didFinishCollectingMetrics:(NSURLSessionTaskMetrics *)metrics;
@end

// 静态C函数，用于对给定的 delegate 类进行一次性的方法交换
static void IMY_Hook_Delegate_Method_Once(Class delegateClass) {
    if (!delegateClass) {
        return;
    }
    
    static NSMutableSet<NSString *> *swizzledClasses = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        swizzledClasses = [NSMutableSet set];
    });
    
    @synchronized(swizzledClasses) {
        NSString *className = NSStringFromClass(delegateClass);
        if ([swizzledClasses containsObject:className]) {
            return;
        }
        
        SEL originalSelector = @selector(URLSession:task:didFinishCollectingMetrics:);
        SEL swizzledSelector = @selector(imy_URLSession:task:didFinishCollectingMetrics:);
        
        Method swizzledMethod = class_getInstanceMethod([IMYMeetyouHTTPHooks class], swizzledSelector);
        if (!swizzledMethod) {
            return;
        }
        
        Method originalMethod = class_getInstanceMethod(delegateClass, originalSelector);
        
        if (originalMethod) {
            method_exchangeImplementations(originalMethod, swizzledMethod);
        } else {
            class_addMethod(delegateClass,
                            originalSelector,
                            method_getImplementation(swizzledMethod),
                            method_getTypeEncoding(swizzledMethod));
        }
        
        [swizzledClasses addObject:className];
    }
}

@implementation IMYMeetyouHTTPHooks (PerformanceMonitoring)

// 这是我们注入的监控方法实现
- (void)imy_URLSession:(NSURLSession *)session task:(NSURLSessionTask *)task didFinishCollectingMetrics:(NSURLSessionTaskMetrics *)metrics {
    [IMYMeetyouHTTPHooks handleTaskMetrics:metrics task:task error:task.error];
    
    // 调用原始实现
    [self URLSession:session task:task
         didFinishCollectingMetrics:metrics];
}

@end

@implementation NSURLSession (IMYPerformanceMonitoring)

+ (void)load {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        SEL originalSelector = @selector(sessionWithConfiguration:delegate:delegateQueue:);
        SEL swizzledSelector = @selector(imy_sessionWithConfiguration:delegate:delegateQueue:);
        
        Method originalMethod = class_getClassMethod(self, originalSelector);
        Method swizzledMethod = class_getClassMethod(self, swizzledSelector);
        
        if (originalMethod && swizzledMethod) {
            method_exchangeImplementations(originalMethod, swizzledMethod);
        }
    });
}

+ (NSURLSession *)imy_sessionWithConfiguration:(NSURLSessionConfiguration *)configuration delegate:(id<NSURLSessionDelegate>)delegate delegateQueue:(NSOperationQueue *)queue {
    if (delegate) {
        IMY_Hook_Delegate_Method_Once([delegate class]);
    }
    
    // 调用原始实现
    return [self imy_sessionWithConfiguration:configuration delegate:delegate delegateQueue:queue];
}

@end
